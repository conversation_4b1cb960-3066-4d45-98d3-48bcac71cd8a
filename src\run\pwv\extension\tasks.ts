import { ObjectId } from "mongodb";
import organizationTasks from "@/app/organizations/riverstar/tasks";

export default {
  ...organizationTasks,

  dbReadProfile: async (profileName) => {
    return await global.db.collection("profiles").findOne({
      profileName: profileName,
    });
  },

  dbReadOffice: async (officeName) => {
    return await global.db.collection("offices").findOne({
      name: officeName,
    });
  },

  dbReadPatient: async (data) => {
    return await global.db.collection("patients").findOne(data);
  },

  dbReadPatientWithEhrId: async (ehrId) => {
    return await global.db.collection("patients").findOne({
      ehrId,
    });
  },

  dbReadBhSeries: async (bhSeriesId?) => {
    if (!bhSeriesId) {
      return await global.db.collection("bhseries").findOne();
    }
    return await global.db.collection("bhseries").findOne({
      _id: new ObjectId(bhSeriesId as string),
    });
  },

  dbReadAllEncounters: async () => {
    return await global.db.collection("encounters").find().toArray();
  },

  dbReadMonitorType: async (name) => {
    return await global.db.collection("monitortypes").findOne({ name });
  },

  dbReadMonitorPackage: async () => {
    return (await global.db.collection("monitorpackage").find().toArray())[0];
  },
};
