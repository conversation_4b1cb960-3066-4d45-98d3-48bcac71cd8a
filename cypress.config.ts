import { MongoClient } from "mongodb";
import { defineConfig } from "cypress";
import fs from "fs-extra";
import loadTasks from "./src/app/tasks";
import parseExcelFile from "./src/app/plugins/getTests/parseExcelFile";
import path from "path";

export default defineConfig({
  e2e: {
    numTestsKeptInMemory: 1,
    fileServerFolder: "./",
    supportFile: "./src/app/e2e.ts",
    specPattern: "./src/app/runSdt.ts",
    excludeSpecPattern: "",
    trashAssetsBeforeRuns: true,
    video: true,
    chromeWebSecurity: false,
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
    baseUrl: "http://localhost:4200",
    viewportWidth: 1920,
    viewportHeight: 1080,
    defaultCommandTimeout: 10000,
    pageLoadTimeout: 10000,
    retries: {
      runMode: 1,
      openMode: 0,
    },
    env: {
      BROWSER: "chrome",
    },

    async setupNodeEvents(on, config) {
      try {
        getAppConfiguration(config);
        setupPaths(config);
        console.log("Configuration loaded:", config.env);
      } catch (error) {
        console.error("Failed to set configuration:", error);
        throw error;
      }

      on("task", await getTasks());

      on("after:screenshot", (details) => {
        return new Promise((resolve) => {
          if (!fs.existsSync(config.env.resultsScreenshotsTempFolder)) {
            fs.mkdirSync(config.env.resultsScreenshotsTempFolder);
          }
          let screenshotPath;
          if (details.testFailure || details.name?.match(/error*.*/)) {
            screenshotPath = `${
              config.env.resultsScreenshotsTempFolder
            }/error-${Date.now()}.png`;
          } else {
            screenshotPath = `${config.env.resultsScreenshotsTempFolder}/${details.name}`;
          }
          fs.moveSync(details.path, screenshotPath, { overwrite: true });
          resolve({ path: screenshotPath });
        });
      });

      await readSdtWorkbookFile(config);

      setWatchers(config);

      return config;
    },
  },
});

interface ConfigEnvironment {
  runFolder?: string;
  configFilePath?: string;
  app?: string;
  sdtFile?: string;
  resultsFolderName?: string;
  resultsFolderPath?: string;
  resultsScreenshotsTempFolder?: string;
  [key: string]: any;
}

interface Config {
  env: ConfigEnvironment;
}

function loadJsonFile(filePath: string): object {
  try {
    const buffer = fs.readFileSync(filePath);
    const jsonFileContent = JSON.parse(buffer.toString());
    return jsonFileContent;
  } catch (error) {
    console.error(`Failed to load JSON file ${filePath}:`, error);
    return {};
  }
}

function getAppConfiguration(config: Config): void {
  const runFolder = process.env.RUN_FOLDER;
  const configPath = process.env.CONFIG_PATH;

  if (!runFolder || !configPath) {
    throw new Error(
      "Required environment variables RUN_FOLDER or CONFIG_PATH are missing"
    );
  }

  const appConfig = loadJsonFile(configPath);

  config.env = {
    ...config.env,
    runFolder,
    configFilePath: configPath,
    ...appConfig,
  };

  if (!config.env.app) {
    config.env.app = process.env.APP;
  }
}

function setupPaths(config: Config): void {
  const { runFolder, app, sdtFile, resultsFolderName } = config.env;

  if (!runFolder) {
    throw new Error("runFolder is not defined in configuration");
  }

  config.env.sdtFilePath = path.resolve(runFolder, sdtFile ?? `${app}.xlsx`);

  config.env.resultsFolderPath = path.resolve(
    runFolder,
    resultsFolderName ?? "results"
  );

  config.env.resultsScreenshotsTempFolder = path.resolve(
    config.env.resultsFolderPath,
    "temp"
  );
}

async function getTasks() {
  let db;
  if (process.env.RSD_DB_URL) {
    const dbUrl = process.env.RSD_DB_URL;
    const mongoClient = await MongoClient.connect(dbUrl as string);
    db = mongoClient.db();
  }
  global.db = db;

  // Dynamically load tasks based on the APP environment variable
  const tasks = await loadTasks();
  return tasks;
}

async function readSdtWorkbookFile(config) {
  await parseExcelFile(config.env.sdtFilePath).then((testsData) => {
    config.env.sdtData = { ...testsData };
  });
}

function setWatchers(config) {
  fs.watchFile(
    config.env.configFilePath,
    {
      persistent: true,
      interval: 1000,
    },
    () => {
      fs.utimesSync("./cypress.config.ts", new Date(), new Date());
    }
  );
  fs.watchFile(
    config.env.sdtFilePath,
    {
      persistent: true,
      interval: 1000,
    },
    () => {
      fs.utimesSync("./cypress.config.ts", new Date(), new Date());
    }
  );
}
