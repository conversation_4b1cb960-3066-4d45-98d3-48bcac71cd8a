@echo off
setlocal enabledelayedexpansion

echo.
echo ======================== Script Driven Tests - Docker Image Setup
echo.

echo.
echo ====== Set configuration values
echo.
@REM Set run folder
set RUN_FOLDER=..

@REM Set config.json path
set CONFIG_PATH=!RUN_FOLDER!\config.json

@REM Set company
for /f "tokens=2 delims=:," %%a in ('type %CONFIG_PATH% ^| findstr /C:"\"company\""') do (
  set "COMPANY=%%a"
)
if not "!COMPANY!"=="" (
  set COMPANY=!COMPANY:~2,-1!
) else (
  set COMPANY=riverstar
)

@REM Set app
for /f "tokens=2 delims=:," %%a in ('type %CONFIG_PATH% ^| findstr /C:"\"app\""') do (
  set "APP=%%a"
)
if not "!APP!"=="" (
  set APP=!APP:~2,-1!
) else (
  for %%F in ("%RUN_FOLDER%") do set APP=%%~nxF
)

@REM Set app repo
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appRepo\""') do (
    set "APP_REPO=%%b"
)
if not "!APP_REPO!"=="" (
  set APP_REPO=!APP_REPO:~2,-2!
  if defined PAT (
    set "APP_REPO=!APP_REPO:$PAT=%PAT%!"
  ) else (
    set "APP_REPO=!APP_REPO:$PAT@=!"
  )
) else if "!COMPANY!"=="riverstar" (
  if defined PAT (
    set "APP_REPO=https://!PAT!@github.com/riverstar/!APP!.git"
  ) else (
    set "APP_REPO=https://github.com/riverstar/!APP!.git"
  )
)

@REM Set app branch
for /f "tokens=2 delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appBranch\""') do (
    set "APP_BRANCH=%%a"
)
if not "!APP_BRANCH!"=="" (
  set APP_BRANCH=!APP_BRANCH:~2,-1!
) else (
  set APP_BRANCH=develop
)

@REM Set package manager
for /f "tokens=2 delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"packageManager\""') do (
    set "PACKAGE_MANAGER=%%a"
)
if not "!PACKAGE_MANAGER!"=="" (
  set PACKAGE_MANAGER=!PACKAGE_MANAGER:~2,-1!
) else (
  set PACKAGE_MANAGER=npm
)

@REM Set SDT repo
if defined PAT (
  set "SDT_REPO=https://!PAT!@github.com/riverstar/sdt.git"
) else (
  set "SDT_REPO=https://github.com/riverstar/sdt.git"
)

@REM Configuration values
echo RUN_FOLDER: %RUN_FOLDER%
echo CONFIG_PATH: %CONFIG_PATH%
echo COMPANY: %COMPANY%
echo APP: %APP%
echo APP_REPO: %APP_REPO%
echo APP_BRANCH: %APP_BRANCH%
echo PACKAGE_MANAGER: %PACKAGE_MANAGER%
echo SDT_REPO: %SDT_REPO%

echo.
echo ====== If the docker image exists, remove all containers and the image
echo.
for /f %%i in ('docker images -q !APP!') do (
  for /f %%j in ('docker ps -a -q --filter "ancestor=!APP!"') do docker rm -f %%j
  docker rmi -f !APP!
)

echo.
echo ====== Setting up DOCKER_BUILD_ARGS
echo.
set "DOCKER_BUILD_ARGS=--no-cache"
set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg PAT=!PAT!"
set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg APP=!APP!"
set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg SDT_REPO=!SDT_REPO!"
if not "!APP_REPO!"=="" (
    set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg APP_REPO=!APP_REPO!"
    set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg APP_BRANCH=!APP_BRANCH!"
    set "DOCKER_BUILD_ARGS=!DOCKER_BUILD_ARGS! --build-arg PACKAGE_MANAGER=!PACKAGE_MANAGER!"
)
echo DOCKER_BUILD_ARGS: !DOCKER_BUILD_ARGS!

echo.
echo ====== Setting up the Docker image
echo.
docker build -t !APP! --no-cache !DOCKER_BUILD_ARGS! -f admin/dockerfile .

echo.
echo Done!
echo Press the Enter key to close the window.
echo.

pause