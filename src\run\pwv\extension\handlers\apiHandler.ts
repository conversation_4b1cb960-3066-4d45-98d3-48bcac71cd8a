import cypressHelper from "@/app/helpers/cypress";

export default {
  createOffice(office) {
    const options = {
      method: "POST",
      url: "app-api/office",
      body: { ...office },
    };
    return cypressHelper.apiCall(options).then((response) => {
      office.responseBody = response.body;
      office.id = office.responseBody._id;
      return office;
    });
  },
  createPatient(patient) {
    const options = {
      method: "POST",
      url: "app-api/patient",
      body: { ...patient },
    };
    return cypressHelper.apiCall(options).then((response) => {
      patient.responseBody = response.body.rsStore;
      patient.id = patient.responseBody._id;
      return patient;
    });
  },
  setCurrentPatient(patient) {
    const options = {
      method: "POST",
      url: "app-api/patient/set-current",
      body: patient,
    };
    return cypressHelper.apiCall(options);
  },
  createBhSeries(bhScreenEncounter) {
    const options = {
      method: "POST",
      url: "app-api/encounter",
      body: bhScreenEncounter,
    };
    return cypressHelper.apiCall(options);
  },
  startScreen(interviewCode) {
    const options = {
      method: "POST",
      url: "app-api/encounter/self-service",
      body: {
        interviewCode: interviewCode,
      },
    };
    return cypressHelper.apiCall(options).then((response) => {
      const body = response.body;
      return body;
    });
  },
  postScreen(screen) {
    const options = {
      method: "POST",
      url: "app-api/screen-shell/self-service",
      body: screen,
    };
    return cypressHelper.apiCall(options);
  },
  reviewScreen(encounter) {
    const options = {
      method: "PUT",
      url: `app-api/encounter/${encounter._id}`,
      body: encounter,
    };
    return cypressHelper.apiCall(options).then((response) => {
      const responseData = response.body.rsStore;
      return responseData;
    });
  },
  startInterview(interviewCode) {
    const options = {
      method: "POST",
      url: "app-api/encounter/self-service",
      body: { interviewCode: interviewCode },
    };
    return cypressHelper.apiCall(options).then((response) => {
      const body = response.body;
      return body;
    });
  },
  sendInterviewAnswer(answer) {
    const options = {
      method: "POST",
      url: "app-api/screen-shell/self-service",
      body: answer,
    };
    return cypressHelper.apiCall(options).then((response) => {
      const body = response.body;
      return body;
    });
  },
  createEncounter(encounter) {
    const options = {
      method: "POST",
      url: "app-api/encounter",
      body: encounter,
    };
    return cypressHelper.apiCall(options).then((response) => {
      encounter = response.body.rsStore;
      return encounter;
    });
  },
  updateEncounter(encounter) {
    const options = {
      method: "PUT",
      url: `app-api/encounter/${encounter._id}`,
      body: encounter,
    };
    return cypressHelper.apiCall(options).then((response) => {
      const encounter = response.body.rsStore;
      return encounter;
    });
  },
  setPatientProvider(providerId) {
    const options = {
      method: "PUT",
      url: `app-api/patient/set-provider/${providerId}`,
      body: {},
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response.body.rsStore);
  },
  setUserAsProvider(userId) {
    const options = {
      method: "PUT",
      url: `app-api/pwvauthusers/${userId}`,
      body: {
        isProvider: true,
      },
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response.body.rsStore);
  },
  selectProvider(providerId) {
    const options = {
      method: "POST",
      url: `app-api/pwvUser/patientProvider/${providerId}`,
    };
    return cypressHelper.apiCall(options);
  },
  createMonitor(monitor) {
    const options = {
      method: "POST",
      url: "app-api/pwvMonitor",
      body: monitor,
    };
    return cypressHelper.apiCall(options);
  },
  createMonitorPackage(packageDate) {
    const auth = Buffer.from("nview:test").toString("base64");
    const options = {
      method: "GET",
      url: `app-api/monitorpackage/trigger-job/${packageDate}`,
      headers: {
        Authorization: `Basic ${auth}`,
      },
    };
    return cypressHelper.apiCall(options);
  },
  createProviderSchedule(providerUserId, startDate, endDate, schedule) {
    const options = {
      method: "POST",
      url: "app-api/providerSchedule",
      body: {
        providerUserId: providerUserId,
        startDate: startDate,
        endDate: endDate,
        schedule: schedule,
      },
    };
    return cypressHelper.apiCall(options);
  },
};
