# Extension Management Simplification

## Overview
This document describes the changes made to simplify the extension management system in the SDT application. The previous system required copying project-specific extension files to a central extension folder during startup. The new system uses dynamic imports to load extensions directly from their project folders.

## Changes Made

### 1. Modified `src/app/sdt.ts`
- **Removed static import**: Replaced `import extension from "@/app/extension/sdt"` with dynamic import logic
- **Added helper functions**:
  - `getProjectName()`: Determines the project name from the `SDT_RUN_FOLDER` environment variable, returns null if not set
  - `loadExtension()`: Dynamically imports the appropriate extension based on the project name, with fallback to default extension
- **Modified constructor**: Split initialization into synchronous constructor and asynchronous `initialize()` method
- **Added error handling**: Falls back to the default extension if project-specific extension fails to load

### 2. Modified `src/app/runSdt.ts`
- **Async initialization**: Wrapped the SDT initialization in an async IIFE (Immediately Invoked Function Expression)
- **Updated flow**: Now calls `sdt.initialize()` before setting up Cypress hooks

### 3. Updated all run scripts (`src/run/*/run.cmd`)
- **Added environment variable**: Set `SDT_RUN_FOLDER=!RUN_FOLDER!` in the environment variables section
- **Removed extension copying**: Replaced the extension folder copying logic with a comment indicating dynamic imports are now used

## How It Works

### Before (Old System)
1. Run script copies `src/run/{project}/extension/*` to `src/app/extension/`
2. SDT application imports from the static path `@/app/extension/sdt`
3. Extension files are overwritten for each project run

### After (New System)
1. Run script sets `SDT_RUN_FOLDER` environment variable to the project's run folder
2. SDT application reads the environment variable to determine the project name
3. If project name is found, dynamically imports from `@/run/{project}/extension/sdt`
4. If no project name or import fails, falls back to `@/app/extension/sdt`

## Benefits

1. **Simplified deployment**: No more file copying during startup
2. **Reduced complexity**: Eliminates the need to manage copied files
3. **Better isolation**: Each project's extension remains in its own folder
4. **Improved reliability**: Fallback mechanism ensures the application still works if a project extension is missing
5. **Easier debugging**: Extension files are always in their original location

## Environment Variable

The system relies on the `SDT_RUN_FOLDER` environment variable to determine which project extension to load:

- **Format**: Full path to the project's run folder (e.g., `C:\Data\rs-projects\sdt\src\run\ce`)
- **Project detection**: Uses `path.basename()` to extract the project name from the folder path
- **Fallback**: If not set or null, loads the static extension from `@/app/extension/sdt`

## Supported Projects

The system automatically detects and loads extensions for:
- `ce` (Customer Experience)
- `billing` (Billing System)
- `jba` (Job Application)
- `pwv` (Patient Workflow Viewer)
- `riverstar` (Riverstar)
- `rwa` (Real World App)
- `srm` (System Resource Management)

## Error Handling

- If a project-specific extension fails to load, the system logs a warning and falls back to the default extension
- The application continues to function normally even if the dynamic import fails
- Console warnings help with debugging extension loading issues

## Migration Notes

- The old `src/app/extension/` folder is still used as a fallback
- Existing project extensions in `src/run/*/extension/` folders work without modification
- Run scripts no longer perform file copying operations
- The system is backward compatible with environments that don't set `SDT_RUN_FOLDER`
