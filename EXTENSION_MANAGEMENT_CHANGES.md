# Extension Management Simplification

## Overview
This document describes the changes made to simplify the extension management system in the SDT application. The previous system required copying project-specific extension files to a central extension folder during startup. The new system uses dynamic imports to load extensions directly from their project folders.

## Changes Made

### 1. Modified `src/app/sdt.ts`
- **Removed static import**: Replaced `import extension from "@/app/extension/sdt"` with dynamic import logic
- **Added private method**:
  - `loadExtension()`: Dynamically imports the appropriate extension based on the `APP` environment variable
- **Modified constructor**: Split initialization into synchronous constructor and asynchronous `initialize()` method
- **Added error handling**: Throws descriptive errors if project extension cannot be loaded or if APP is not set
- **Improved encapsulation**: Extension loading logic is now contained within the Sdt class as private methods

### 2. Modified `src/app/runSdt.ts`
- **Async initialization**: Wrapped the SDT initialization in an async IIFE (Immediately Invoked Function Expression)
- **Updated flow**: Now calls `sdt.initialize()` before setting up Cypress hooks
- **Added error handling**: Catches and logs initialization errors with descriptive messages

### 3. Updated all run scripts (`src/run/*/run.cmd`)
- **Removed extension copying**: Replaced the extension folder copying logic with a comment indicating dynamic imports are now used
- **Uses existing APP variable**: Leverages the already available `APP` environment variable that contains the project name

### 4. Removed static extension folder
- **Deleted `src/app/extension/`**: Completely removed the static extension folder and all its contents
- **No fallback**: System now requires a valid project extension or will fail with descriptive error messages

## How It Works

### Before (Old System)
1. Run script copies `src/run/{project}/extension/*` to `src/app/extension/`
2. SDT application imports from the static path `@/app/extension/sdt`
3. Extension files are overwritten for each project run

### After (New System)
1. Run script already has `APP` environment variable set to the project name
2. SDT application reads the `APP` environment variable directly
3. If project name is found, dynamically imports from `@/run/{project}/extension/sdt`
4. If no project name is set or import fails, throws descriptive error and stops execution

## Benefits

1. **Simplified deployment**: No more file copying during startup
2. **Reduced complexity**: Eliminates the need to manage copied files and static extension folder
3. **Better isolation**: Each project's extension remains in its own folder
4. **Cleaner architecture**: No fallback dependencies or unused static files
5. **Easier debugging**: Extension files are always in their original location
6. **Fail-fast behavior**: Clear error messages when configuration is incorrect
7. **Better encapsulation**: Extension loading logic is properly contained within the Sdt class
8. **Improved testability**: Private methods can be tested independently if needed

## Environment Variable

The system relies on the existing `APP` environment variable to determine which project extension to load:

- **Format**: Simple project name (e.g., `ce`, `billing`, `jba`)
- **Direct usage**: No path parsing needed - the variable contains exactly what we need
- **Already available**: Uses the existing `APP` variable that's already set by all run scripts
- **Required**: Must be set for the application to run - no fallback behavior

## Supported Projects

The system automatically detects and loads extensions for:
- `ce` (Customer Experience)
- `billing` (Billing System)
- `jba` (Job Application)
- `pwv` (Patient Workflow Viewer)
- `riverstar` (Riverstar)
- `rwa` (Real World App)
- `srm` (System Resource Management)

## Error Handling

- If `APP` is not set, throws an error with instructions to run from a project-specific script
- If a project-specific extension fails to load, throws an error with the project name and file path
- Clear error messages help with debugging configuration and extension loading issues
- Fail-fast behavior prevents the application from running with incorrect configuration

## Migration Notes

- **Breaking Change**: The `src/app/extension/` folder has been completely removed
- Existing project extensions in `src/run/*/extension/` folders work without modification
- Run scripts no longer perform file copying operations
- **Simplified**: Uses the existing `APP` environment variable that's already available in all run scripts
- **No additional setup**: No need to set additional environment variables or parse paths
