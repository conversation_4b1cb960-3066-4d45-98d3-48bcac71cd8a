import User from "@/app/organizations/riverstar/domain/user";

export default class Provider extends User {
  constructor(data) {
    super(data);
  }

  create() {
    return super.create()
      .then(() => {
        Cypress.sdt.apiHandler.login(
          Cypress.sdt.config.admin.username,
          Cypress.sdt.config.admin.password
        );
        Cypress.sdt.apiHandler.setUserAsProvider(
          Cypress.sdt.domain.data.provider.id
        );
      });
  }
}
