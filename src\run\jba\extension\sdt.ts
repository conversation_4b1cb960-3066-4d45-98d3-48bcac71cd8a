import cypressHelper from "@/app/helpers/cypress";

export default class Sdt {
  constructor() {}

  items = {
    "Jobs List Toolbar": {
      selector: "app-tool-bar",
    },
    "Pagination Details": {
      selector: ".pagination-details",
    },
    Snackbar: {
      selector: "simple-snack-bar span",
    },
    "Snackbar Button": {
      selector: "simple-snack-bar button",
    },
    Table: {
      selector: "mat-table",
    },
    "Table Column": {
      selector: "mat-cell",
    },
    "Table Column Header": {
      selector: "mat-header-cell",
    },
    "Table Row": {
      selector: "mat-row",
    },
  };

  apiHandler = {
    loginAdmin() {
      const apiEndpoint =
        "https://demo1.jobboardaggregator.com/api/prism/login";
      const requestBody = {
        method: "POST",
        url: apiEndpoint,
        body: {
          email: "<EMAIL>",
          password: "Prism123!",
        },
      };
      return cy
        .request(requestBody)
        .then((response) => response.body.data.security_token.token);
    },

    getAllUsers(securityToken) {
      const requestBody = {
        method: "GET",
        url: "https://demo1.jobboardaggregator.com/api/prism/users?archived=false&page=1&count=100&sortBy=first_name&sortDirection=asc&search=%7B%7D",
        cache: false,
        headers: {
          "Security-token": securityToken,
        },
      };
      return cypressHelper.apiCall(requestBody);
    },

    addUser(securityToken, user) {
      const requestBody = {
        method: "POST",
        url: `https://demo1.jobboardaggregator.com/api/prism/users`,
        headers: {
          "Security-token": securityToken,
        },
        body: user,
      };
      return cypressHelper.apiCall(requestBody);
    },

    deleteUser(securityToken, user) {
      const requestBody = {
        method: "DELETE",
        url: `https://demo1.jobboardaggregator.com/api/prism/users/${user.id}`,
        headers: {
          "Security-token": securityToken,
        },
      };
      return cypressHelper.apiCall(requestBody);
    },
  };

  actions = {
    "Login Admin": () => {
      this.apiHandler
        .loginAdmin()
        .then((token) => (Cypress.sdt.config.securityToken = token));
      cy.window().then((window) => {
        window.localStorage.setItem(
          "token",
          "U2FsdGVkX18nQWj9Jf1G+BKS3WmCDsXtcKEhBaqCuYSApihUmQLoL4GqGWRf4xwNupRYSaNj+n1BJ0vZf/UMAKhkx3UGS5jZe8FKzSrXM2c="
        );
      });
    },
    "Add User": () => {
      const user = Cypress.sdt.current.step.simpleValues[0];
      const userToAdd = {
        first_name: user["First Name"],
        last_name: user["Last Name"],
        email: user["Email"],
        name_suffix: null,
        address_1: user["Address"]["Address Line 1"],
        address_2: user["Address"]["Address Line 2"],
        city: user["Address"]["City"],
        state: user["Address"]["State"],
        zip: user["Address"]["Zip"],
        phone_number: user["Phone 1"],
        notes: user["Notes"],
        password: user["Password"],
        user_item_ids: [1],
        user_group_ids: [],
      };
      this.apiHandler.addUser(Cypress.sdt.config.securityToken, userToAdd);
    },
    "Delete All Users": () => {
      this.apiHandler
        .getAllUsers(Cypress.sdt.config.securityToken)
        .then((response) =>
          response.body.data
            .filter((user) => user.email !== "<EMAIL>")
            .forEach((userToDelete) =>
              this.apiHandler.deleteUser(
                Cypress.sdt.config.securityToken,
                userToDelete
              )
            )
        );
    },
  };

  setup = {
    beforeEach() {
      cy.viewport(1920, 1080);
    },
  };
}
