import Provider from "./provider";
import h from "@/app/helpers/all";

export default class Patient {
  id = "";
  ehrId = "";
  providerUserId = null;
  firstName = "";
  lastName = "";
  displayName = "";
  notificationDisplayName = "";
  email = "";
  address = "";
  address2 = "";
  city = "";
  state = "";
  zip = "";
  dob = "";
  gender = "";
  contactPreference = "";
  insuranceType = "";
  imported: boolean = false;
  isActive: true;
  phoneList = [];

  constructor(data, randomize = false) {
    Object.assign(this, h.parseObjectWithTemplate(data, this));
    if (randomize)
      this.ehrId = Math.floor(Math.random() * 900000 + 100000).toString();
    if (data["Provider"]) {
      Cypress.sdt.domain.data.provider = new Provider(data["Provider"])
    };
    if (data["Dob"])
      this.dob = h.convertUtcDateToIsoFormat(data["Dob"], "mm/dd/yyyy");
    this.gender = this.gender.toLowerCase().trim();
    this.contactPreference = this.contactPreference.toLowerCase().trim();
    this.insuranceType = this.insuranceType.toLowerCase().trim();
    this.address = data["Full Address"]["Address"];
    this.address2 = data["Full Address"]["Address (cont.)"] ?? "";
    this.city = data["Full Address"]["City"] ?? "";
    this.state = data["Full Address"]["State"] ?? "";
    this.zip = data["Full Address"]["Zip"] ?? "";
    if (h.isString(data["Phone"])) {
      this.phoneList.push({
        phoneType: "Home",
        phone: data["Phone"],
      });
    } else if (data["Phone List"]) {
      data["Phone List"]?.forEach((dataPhone) => {
        const phoneToAdd = {};
        phoneToAdd["phoneType"] = dataPhone["Phone Type"];
        phoneToAdd["phone"] = dataPhone["Raw Number"];
        this.phoneList.push(phoneToAdd);
      });
    }
  }

  create() {
    return cy
      .then(() => {
        if (Cypress.sdt.domain.data.provider)
          Cypress.sdt.domain.data.provider.create().then(() => {
            this.providerUserId = Cypress.sdt.domain.data.provider.id;
          });
      })
      .then(() =>
        Cypress.sdt.apiHandler.login(
          Cypress.sdt.config.admin.username,
          Cypress.sdt.config.admin.password
        )
      )
      .then(() => Cypress.sdt.apiHandler.createPatient(this));
  }
}
