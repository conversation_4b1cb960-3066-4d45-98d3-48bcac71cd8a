# Use the official Ubuntu base image
FROM ubuntu:20.04 AS base

# Set environment variable to avoid interactive installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies, including OpenSSL 1.1 for libcrypto.so.1.1
RUN apt-get update && apt-get install -y \
  wget \
  gnupg \
  software-properties-common \
  git \
  curl \
  libssl1.1 \
  && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
  && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list' \
  && apt-get update \
  && apt-get install -y google-chrome-stable \
  && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
  && apt-get install -y nodejs --no-install-recommends

# Set environment variables for Chrome
ENV CHROME_BIN=/usr/bin/google-chrome-stable

# Set npm registry
ARG PAT
RUN echo //npm.pkg.github.com/:_authToken=$PAT >> /root/.npmrc
RUN echo @riverstar:registry=https://npm.pkg.github.com >> /root/.npmrc
RUN echo @riverstar:always-auth=true >> /root/.npmrc
RUN echo strict-ssl=false >> /root/.npmrc
RUN echo timeout=120000 >> /root/.npmrc

# Install Yarn if specified
ARG PACKAGE_MANAGER
RUN if [ "$PACKAGE_MANAGER" = "yarn" ]; then \
  curl -o- -L https://yarnpkg.com/install.sh | bash; \
  fi

# Clone the app repository and install it, if specified
ARG APP
ARG APP_REPO
ARG APP_BRANCH
ARG PACKAGE_MANAGER
WORKDIR /home
RUN if [ ! -z "$APP_REPO" ]; then \
  git clone $APP_REPO; \
  cd $APP; \
  git switch $APP_BRANCH; \
  npm i -g concurrently; \
  npm i -g cross-env; \
  if [ "$PACKAGE_MANAGER" = "yarn" ]; then \
    /root/.yarn/bin/yarn install; \
  fi; \
  if [ "$PACKAGE_MANAGER" = "npm" ]; then \
    npm i -D --no-save --lock=false; \
  fi; \
  fi

# Clone the SDT repository and install it
WORKDIR /home
ARG SDT_REPO
RUN git clone $SDT_REPO
WORKDIR /home/<USER>
RUN npm i

# Set the display property to work with the XWin client
ENV DISPLAY=host.docker.internal:0.0

# Set the working directory
WORKDIR /home