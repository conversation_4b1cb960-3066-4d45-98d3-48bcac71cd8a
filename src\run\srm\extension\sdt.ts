import extensionActions from "./core/actions";
import extensionApiHandler from "./handlers/apiHandler";
import extensionIcons from "./core/icons";
import extensionSetup from "./core/setup";
import h from "@/app/helpers/all";
import riverstarSdt from "@/app/organizations/riverstar/sdt";

export default {
  setup: extensionSetup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, extensionIcons),
  actions: { ...riverstarSdt.actions, ...extensionActions },
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, extensionApiHandler),
} as const;
