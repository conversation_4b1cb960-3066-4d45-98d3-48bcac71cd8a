import fs from "fs-extra";
import organizationTasks from "@/app/organizations/riverstar/tasks";
import path from "path";

export default {
  ...organizationTasks,
  
  dbImportCollections: async () => {
    const modelFolderPath = path.join("../ce/model");

    const profilesFilePath = path.join(modelFolderPath, "setup/profiles.json");
    let data = fs.readFileSync(profilesFilePath);
    let profiles = JSON.parse(data.toString());
    profiles = profiles
      .map((profile) => {
        delete profile._id;
        delete profile.createdAt;
        delete profile.updatedAt;
        return profile;
      })
      .filter((profile) => profile.profileName !== "Admin");
    await global.db.collection("profiles").insertMany(profiles);

    const templatesFilePath = path.join(
      modelFolderPath,
      "setup/template-admin.json"
    );
    data = fs.readFileSync(templatesFilePath);
    let templates = JSON.parse(data.toString());
    templates = templates.map((template) => {
      delete template._id;
      delete template.createdAt;
      delete template.updatedAt;
      return template;
    });
    await global.db.collection("templateAdmin").insertMany(templates);

    const agenciesFilePath = path.join(modelFolderPath, "setup/agencies.json");
    data = fs.readFileSync(agenciesFilePath);
    let agencies = JSON.parse(data.toString());
    agencies = agencies.map((agency) => {
      delete agency._id;
      delete agency.createdAt;
      delete agency.updatedAt;
      return agency;
    });
    return await global.db.collection("agencies").insertMany(agencies);
  },
};
