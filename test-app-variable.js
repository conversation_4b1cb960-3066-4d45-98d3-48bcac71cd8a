// Test script to verify the APP variable approach works correctly
class MockSdt {
  constructor() {
    this.config = {};
  }

  async loadExtension() {
    const projectName = process.env.APP;

    if (!projectName) {
      throw new Error(
        "APP environment variable is not set. " +
          "Please run SDT from a project-specific run script that sets this variable."
      );
    }

    // Simulate checking if extension exists
    const validProjects = ['ce', 'billing', 'jba', 'pwv', 'riverstar', 'rwa', 'srm'];
    
    if (validProjects.includes(projectName)) {
      return {
        projectName,
        importPath: `@/run/${projectName}/extension/sdt`,
        config: { projectSpecific: true }
      };
    } else {
      throw new Error(
        `Failed to load extension for project '${projectName}'. ` +
          `Please ensure the extension exists at src/run/${projectName}/extension/sdt.ts.`
      );
    }
  }

  async initialize() {
    try {
      this.extension = await this.loadExtension();
      console.log(`✅ Successfully loaded extension for project: ${this.extension.projectName}`);
      return this;
    } catch (error) {
      console.log(`❌ Failed to initialize: ${error.message}`);
      throw error;
    }
  }
}

// Test cases using APP environment variable directly
const testCases = [
  { name: 'CE Project', app: 'ce' },
  { name: 'Billing Project', app: 'billing' },
  { name: 'JBA Project', app: 'jba' },
  { name: 'No Environment', app: undefined },
  { name: 'Invalid Project', app: 'nonexistent' },
];

console.log('=== Testing APP Variable Approach ===\n');

async function runTests() {
  for (const testCase of testCases) {
    console.log(`--- Testing ${testCase.name} ---`);
    
    // Set APP environment variable for this test
    const originalEnv = process.env.APP;
    process.env.APP = testCase.app;
    
    try {
      const sdt = new MockSdt();
      await sdt.initialize();
    } catch (error) {
      // Error already logged in initialize method
    }
    
    // Restore original environment
    process.env.APP = originalEnv;
    console.log('');
  }
}

runTests().then(() => {
  console.log('=== APP Variable Test Complete ===');
});
