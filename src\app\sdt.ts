import Domain from "@/app/core/domain";
import Report from "@/app/core/report";
import Test from "@/app/core/test";
import coreActions from "@/app/core/actions";
import corePrimitives from "@/app/core/primitives";
import path from "path";

export default class Sdt {
  extension;
  config;
  setup;
  icons;
  actions;
  primitives;
  apiHandler;
  spyHandler;
  apiInterceptors;
  data = {
    tests: [],
    scripts: [],
    tables: [],
    aliases: {},
    elements: {},
  };
  testsToRun;
  domain;
  current = {
    sheet: "",
    test: {},
    step: {},
    stepIndex: -1,
  };
  results = {
    executedTests: [],
    testsWithError: [],
    notCompletedTests: [],
    omittedTests: [],
    omittedSteps: [],
    usedAliases: [],
    usedElements: [],
  };
  report;

  constructor() {
    // Initialize with basic Cypress config first
    this.config = {
      ...Cypress.env(),
    };
  }

  /**
   * Determines the project name from the SDT_RUN_FOLDER environment variable
   * Returns null if not found
   */
  private getProjectName(): string | null {
    const runFolder = process.env.SDT_RUN_FOLDER;
    if (runFolder) {
      // Extract the project name from the run folder path
      // e.g., "C:\Data\rs-projects\sdt\src\run\ce" -> "ce"
      return path.basename(runFolder);
    }
    return null;
  }

  /**
   * Dynamically imports the extension based on the project name
   */
  private async loadExtension() {
    const projectName = this.getProjectName();

    if (!projectName) {
      throw new Error(
        "SDT_RUN_FOLDER environment variable is not set. " +
          "Please run SDT from a project-specific run script that sets this variable."
      );
    }

    try {
      const extension = await import(`@/run/${projectName}/extension/sdt`);
      return extension.default;
    } catch (error) {
      throw new Error(
        `Failed to load extension for project '${projectName}'. ` +
          `Please ensure the extension exists at src/run/${projectName}/extension/sdt.ts. ` +
          `Original error: ${error.message}`
      );
    }
  }

  /**
   * Async initialization method that loads the extension and sets up the instance
   */
  async initialize() {
    this.extension = await this.loadExtension();

    // Update config with extension config
    this.config = {
      ...this.config,
      ...this.extension?.config,
    };

    this.setup = this.extension?.setup;
    this.icons = this.extension?.icons;
    this.actions = {
      ...coreActions,
      ...this.extension?.actions,
    };
    this.primitives = {
      ...corePrimitives,
      ...this.extension?.primitives,
    };
    this.apiHandler = this.extension?.["apiHandler"];
    this.spyHandler = this.extension?.["spyHandler"];
    this.apiInterceptors = this.extension?.["apiInterceptors"];

    this.data.tests = structuredClone(this.config.sdtData.tests);
    this.data.scripts = structuredClone(this.config.sdtData.scripts);
    this.data.tables = structuredClone(this.config.sdtData.tables);
    this.data.aliases = structuredClone(this.config.sdtData.aliases);
    this.data.elements = structuredClone(this.config.sdtData.elements);

    this.report = new Report(this.config);

    this.setTests();

    return this;
  }

  setTests() {
    this.data.tests.forEach((test) => {
      if (test["runFlag"] === "all") {
        this.data.tests
          .filter((t) => t["sheet"] === test["sheet"])
          .forEach((t) => (t["runFlag"] = "z"));
      }
      if (test["runFlag"] === "none") {
        this.data.tests
          .filter((t) => t["sheet"] === test["sheet"])
          .forEach((t) => (t["runFlag"] = null));
      }
    });
    const zPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "z"
    );
    const yPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "y"
    );
    const xPriorityTests = this.data.tests.filter(
      (test) => test["runFlag"] === "x"
    );
    const omittedTests = this.data.tests.filter((test) => !test["runFlag"]);
    if (zPriorityTests.length) {
      this.testsToRun = [...zPriorityTests];
      this.results.omittedTests = [
        ...xPriorityTests,
        ...yPriorityTests,
        ...omittedTests,
      ];
    } else if (yPriorityTests.length) {
      this.testsToRun = [...yPriorityTests, ...zPriorityTests];
      this.results.omittedTests = [...xPriorityTests, ...omittedTests];
    } else {
      this.testsToRun = [...xPriorityTests];
      this.results.omittedTests = omittedTests;
    }
  }

  resetDomain() {
    const entities = Object.values(this.data.tables).reduce((acc, table) => {
      return {
        ...(acc as object),
        ...(table as object),
      };
    }, {});
    this.domain = new Domain(entities);
  }

  run() {
    const sheets = [...new Set(this.testsToRun.map((test) => test.sheet))];
    sheets.forEach((sheetName: string) => {
      const sheetTests = this.testsToRun.filter(
        (test) => test.sheet === sheetName
      );
      describe(sheetName, () => {
        sheetTests.forEach((testData) => {
          const test = new Test(testData);
          test.run();
        });
      });
    });
  }
}
