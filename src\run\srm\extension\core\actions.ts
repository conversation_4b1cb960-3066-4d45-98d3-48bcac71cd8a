import Agency from "../domain/agency";
import Client from "../domain/client";
import srmUser from "../domain/user";

export default {
  "Create User": function (
    userData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cy.wrap(this["Create Agency"](userData["Agency"])).then(() => {
      Cypress.sdt.domain.data.user = new srmUser(userData);
      return Cypress.sdt.domain.data.user.create();
    });
  },
  "Create User and Login": function (
    userData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    cy.wrap(this["Create User"](userData)).then(() => {
      Cypress.sdt.domain.data.user.login();
    });
  },
  "Create Agency": function (
    agencyData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    Cypress.sdt.domain.data.agency = new Agency(agencyData);
    Cypress.sdt.domain.data.agency.create();
  },
  "Create Client": function (
    clientData = Cypress.sdt.current.step.simpleValues[0]
  ) {
    Cypress.sdt.domain.data.client = new Client(clientData);
    Cypress.sdt.domain.data.client.create();
  },
};
