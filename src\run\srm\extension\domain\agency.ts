import h from "@/app/helpers/all";

export default class Agency {
  id = "";
  externalAgencyCode = "";
  name = "";
  phone = "";
  address = "";
  address2 = "";
  city = "";
  state = "";
  zip = "";
  region = "";
  isActive = true;
  isPartner = false;
  isAddressConfidential = false;
  notes = "";
  type = "CBO";
  actions = [];
  areas = [];
  receiveOutsideScreenings = false;
  receiveElectronicReferrals = false;
  isCareHubResource = false;
  isSelfServiceAgency = false;
  isHospitalNavigator = false;
  notificationType = "no";
  notificationAssessmentType = "no";
  escalationToggle = true;
  agencyUsers = [];
  unprotectedName = "";
  email = "";
  url = "";
  isCareTeamAvailable = false;
  externalPendingAssessments = false;
  redirectAssessments = null;
  delegatedAgencyId = null;
  agencyPrograms = [];
  escalationList = [];
  escalationDays = null;
  usersReferNotificationList = [];
  usersAssessmentNotificationList = [];

  constructor(agency) {
    Object.assign(this, h.parseObjectWithTemplate(agency, this));
  }

  create() {
    return Cypress.sdt.apiHandler
      .login(
        Cypress.sdt.config.admin.username,
        Cypress.sdt.config.admin.password
      )
      .then(() => Cypress.sdt.apiHandler.createAgency(this))
      .then((agency) => (this.id = agency["_id"]));
  }
}
