@echo off
setlocal enabledelayedexpansion

echo.
echo ======================== Script Driven Tests - Docker Environment
echo.

echo.
echo ====== Set configuration values
echo.
cd ..

@REM Set config.json path
set "CONFIG_PATH=config.json"

@REM Set company
for /f "tokens=2 delims=:," %%a in ('type %CONFIG_PATH% ^| findstr /C:"\"company\""') do (
  set "COMPANY=%%a"
)
if not "!COMPANY!"=="" (
  set "COMPANY=!COMPANY:~2,-1!"
) else (
  set "COMPANY=riverstar"
)

@REM Set app
for /f "tokens=2 delims=:," %%a in ('type %CONFIG_PATH% ^| findstr /C:"\"app\""') do (
  set "APP=%%a"
)
if not "!APP!"=="" (
  set "APP=!APP:~2,-1!"
) else (
  for %%F in ("%cd%") do set "APP=%%~nxF"
)

@REM Set app folder
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appFolder\""') do (
    set "APP_FOLDER=%%b"
)
if not "!APP_FOLDER!"=="" (
  set "APP_FOLDER=!APP_FOLDER:~1,-1!"
) else (
  if "!COMPANY!"=="riverstar" (
    set "APP_FOLDER=/home/<USER>"
  )
)

@REM Set app repo
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appRepo\""') do (
    set "APP_REPO=%%b"
)
if not "!APP_REPO!"=="" (
  set "APP_REPO=!APP_REPO:~2,-2!"
  if defined PAT (
    set "APP_REPO=!APP_REPO:$PAT=%PAT%!"
  ) else (
    set "APP_REPO=!APP_REPO:$PAT@=!"
  )
) else if "!COMPANY!"=="riverstar" (
  if defined PAT (
    set "APP_REPO=https://!PAT!@github.com/riverstar/!APP!.git"
  ) else (
    set "APP_REPO=https://github.com/riverstar/!APP!.git"
  )
)

@REM Set app branch
for /f "tokens=2 delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appBranch\""') do (
    set "APP_BRANCH=%%a"
)
if not "!APP_BRANCH!"=="" (
  set "APP_BRANCH=!APP_BRANCH:~2,-1!"
) else (
  set "APP_BRANCH=develop"
)

@REM Set DB url
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"dbUrl\""') do (
    set "DB_URL=%%b"
)
if not "!DB_URL!"=="" (
  set "DB_URL=!DB_URL:~2,-2!"
  set "DB_URL=!DB_URL:$PAT=%PAT%!"
) else if "!COMPANY!"=="riverstar" (
  set "DB_URL=mongodb://127.0.0.1:27055/inMemoryDB"
)

@REM Set package manager
for /f "tokens=2 delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"packageManager\""') do (
    set "PACKAGE_MANAGER=%%a"
)
if not "!PACKAGE_MANAGER!"=="" (
  set "PACKAGE_MANAGER=!PACKAGE_MANAGER:~2,-1!"
) else (
  set "PACKAGE_MANAGER=npm"
)

@REM Set app url
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appUrl\""') do (
    set "APP_URL=%%b"
)
if not "!APP_URL!"=="" (
  set "APP_URL=!APP_URL:~2,-2!"
  set "APP_URL=!APP_URL:$PAT=%PAT%!"
) else (
  set "APP_URL=http://localhost:4200"
)

@REM Set app script
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"appScript\""') do (
    set "APP_SCRIPT=%%b"
)
if not "!APP_SCRIPT!"=="" (
  set "APP_SCRIPT=!APP_SCRIPT:~2,-1!"
)

@REM Set SDT folder
for /f "tokens=1* delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"sdtFolder\""') do (
    set "SDT_FOLDER=%%b"
)
if not "!SDT_FOLDER!"=="" (
  set "SDT_FOLDER=!SDT_FOLDER:~1,-1!"
) else (
    set "SDT_FOLDER=/home/<USER>"
)

@REM Set SDT script
for /f "tokens=2 delims=:," %%a in ('type "%CONFIG_PATH%" ^| findstr /C:"\"sdtScript\""') do (
    set "SDT_SCRIPT=%%a"
)
if not "!SDT_SCRIPT!"=="" (
  set "SDT_SCRIPT=!SDT_SCRIPT:~2,-1!"
) else (
  set "SDT_SCRIPT=npx cypress open"
)

@REM Configuration values
echo CONFIG_PATH: %CONFIG_PATH%
echo COMPANY: %COMPANY%
echo APP: %APP%
echo APP_FOLDER: %APP_FOLDER%
echo APP_REPO: %APP_REPO%
echo APP_BRANCH: %APP_BRANCH%
echo DB_URL: %DB_URL%
echo PACKAGE_MANAGER: %PACKAGE_MANAGER%
echo APP_URL: %APP_URL%
echo APP_SCRIPT: %APP_SCRIPT%
echo SDT_FOLDER: %SDT_FOLDER%
echo SDT_SCRIPT: %SDT_SCRIPT%

echo.
echo ====== Remove all containers created from the project image
echo.
for /f %%i in ('docker ps -a -q --filter "ancestor=%APP%"') do docker rm -f %%i

echo.
echo ====== Create the Docker container and run start up script
echo.
docker run --rm ^
  -e "RUN_FOLDER=/home/<USER>" ^
  -e "CONFIG_PATH=/home/<USER>/config.json" ^
  -e "COMPANY=%COMPANY%" ^
  -e "APP=%APP%" ^
  -e "APP_FOLDER=%APP_FOLDER%" ^
  -e "APP_REPO=%APP_REPO%" ^
  -e "PACKAGE_MANAGER=%PACKAGE_MANAGER%" ^
  -e "APP_URL=%APP_URL%" ^
  -e "APP_SCRIPT=%APP_SCRIPT%" ^
  -e "SDT_FOLDER=%SDT_FOLDER%" ^
  -e "SDT_SCRIPT=%SDT_SCRIPT%" ^
  -e "CYPRESS_BASE_URL=%APP_URL%" ^
  -e "RSD_DB_URL=%DB_URL%" ^
  --mount type=bind,source=%cd%,target=/home/<USER>/home/<USER>/docker/admin/start.sh

@REM docker run -it --rm --mount type=bind,source=%cd%,target=/home/<USER>/bin/bash

echo.
echo Done!
echo Press the Enter key to close the window.
echo.

pause