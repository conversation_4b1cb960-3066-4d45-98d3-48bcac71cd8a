[{"_id": "67b77a6c96736e1623c949f3", "profileName": "Admin", "menuItems": [{"menuTitle": "Administration", "menuRoute": "", "menuItems": [{"menuTitle": "Profiles", "menuRoute": "profiles/list"}, {"menuTitle": "Users", "menuRoute": "user-admin/list"}, {"menuTitle": "Usage Report", "menuRoute": "usage-report"}]}, {"menuTitle": "Introduction", "menuRoute": "app/introduction"}, {"menuTitle": "Getting started", "menuItems": [{"menuTitle": "Install riverstar dependencies", "menuRoute": "app/getting-started/install-rs-deps"}, {"menuTitle": "Setup", "menuRoute": "app/getting-started/setup"}, {"menuTitle": "<PERSON>er", "menuRoute": "app/getting-started/docker"}]}, {"menuTitle": "@riverstar/cli", "menuItems": [{"menuTitle": "Overview", "menuRoute": "app/cli/overview"}]}, {"menuTitle": "@riverstar/desktop", "menuRoute": "", "menuItems": [{"menuTitle": "Rs Form Group", "menuRoute": "app/desktop/rs-form-group"}, {"menuTitle": "<PERSON> Spinner", "menuRoute": "app/desktop/rs-spinner"}, {"menuTitle": "Rs Table", "menuRoute": "app/desktop/rs-table"}, {"menuTitle": "Storage Service", "menuRoute": "app/desktop/storage-service"}, {"menuTitle": "Country Service", "menuRoute": "app/desktop/country-service"}, {"menuTitle": "Terms & conditions", "menuRoute": "app/desktop/terms-conditions"}, {"menuTitle": "Address", "menuRoute": "app/desktop/address"}, {"menuTitle": "Rs Date Pipe", "menuRoute": "app/desktop/rs-date-pipe"}, {"menuTitle": "Rs Debounce Click Directive", "menuRoute": "app/desktop/rs-debounce-click"}, {"menuTitle": "Rs Input Trim Directive", "menuRoute": "app/desktop/rs-input-trim"}, {"menuTitle": "Masks", "menuRoute": "app/desktop/masks"}, {"menuTitle": "Reference Documents", "menuRoute": "ref-doc/list"}]}, {"menuTitle": "@riverstar/desktop-api", "menuRoute": "", "menuItems": [{"menuTitle": "Audit", "menuRoute": "app/desktop-api/audit"}, {"menuTitle": "Logging", "menuRoute": "app/desktop-api/logging"}, {"menuTitle": "Pdf Builder", "menuRoute": "app/desktop-api/pdf-builder"}, {"menuTitle": "Csv Builder", "menuRoute": "app/desktop-api/csv-builder"}, {"menuTitle": "Rs schema", "menuRoute": "app/desktop-api/rs-schema"}, {"menuTitle": "Transactions", "menuRoute": "app/desktop-api/transactions"}]}, {"menuTitle": "@riverstar/workflow-studio", "menuRoute": "", "menuItems": [{"menuTitle": "Getting started", "menuRoute": "app/workflow-studio/getting-started"}, {"menuTitle": "Setup", "menuRoute": "app/workflow-studio/setup"}, {"menuTitle": "Elements", "menuRoute": "workflow-studio/element"}, {"menuTitle": "Workflows", "menuRoute": "workflow-studio/workflow/list"}, {"menuTitle": "Workflow Profiles", "menuRoute": "workflow-studio/workflow-profile/list"}, {"menuTitle": "Business rules", "menuRoute": "workflow-studio/business-rule/list"}, {"menuTitle": "Entities", "menuRoute": "workflow-studio/entity"}, {"menuTitle": "Dynamic languages", "menuRoute": "workflow-studio/dynamic-languages/list"}, {"menuTitle": "Security model", "menuRoute": "workflow-studio/security-model/list"}, {"menuTitle": "Settings", "menuRoute": "workflow-studio/settings"}, {"menuTitle": "Stylesheets", "menuRoute": "workflow-studio/stylesheet/list"}]}, {"menuTitle": "@riverstar/campaign-manager", "menuRoute": "", "menuItems": [{"menuTitle": "Getting started", "menuRoute": "app/campaign-manager/getting-started"}, {"menuTitle": "Setup", "menuRoute": "app/campaign-manager/setup"}, {"menuTitle": "Campaigns", "menuRoute": "/desktop/campaign-manager/list"}, {"menuTitle": "Report", "menuRoute": "/desktop/campaign-manager/report"}]}, {"menuTitle": "Migrations", "menuRoute": "", "menuItems": [{"menuTitle": "Migration to Desktop v7", "menuRoute": "app/migrations/migration"}]}, {"menuTitle": "FAQ", "menuRoute": "app/faq"}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "app/introduction", "createdAt": "2025-02-20T18:54:36.894Z", "updatedAt": "2025-02-20T18:54:36.894Z", "__v": 0}, {"_id": "67b77a6c96736e1623c949f4", "profileName": "Base User", "menuItems": [{"menuTitle": "Introduction", "menuRoute": "app/introduction"}, {"menuTitle": "Getting started", "menuItems": [{"menuTitle": "Install riverstar dependencies", "menuRoute": "app/getting-started/install-rs-deps"}, {"menuTitle": "Setup", "menuRoute": "app/getting-started/setup"}, {"menuTitle": "<PERSON>er", "menuRoute": "app/getting-started/docker"}]}, {"menuTitle": "@riverstar/cli", "menuItems": [{"menuTitle": "Overview", "menuRoute": "app/cli/overview"}]}, {"menuTitle": "@riverstar/desktop", "menuItems": [{"menuTitle": "Rs Form Group", "menuRoute": "app/desktop/rs-form-group"}, {"menuTitle": "<PERSON> Spinner", "menuRoute": "app/desktop/rs-spinner"}, {"menuTitle": "Rs Table", "menuRoute": "app/desktop/rs-table"}, {"menuTitle": "Storage Service", "menuRoute": "app/desktop/storage-service"}, {"menuTitle": "Countries Service", "menuRoute": "app/desktop/countries-service"}, {"menuTitle": "Terms & conditions", "menuRoute": "app/desktop/terms-conditions"}, {"menuTitle": "RsDatePipe", "menuRoute": "app/desktop/rs-date-pipe"}, {"menuTitle": "Reference Documents", "menuRoute": "ref-doc/list"}]}, {"menuTitle": "@riverstar/desktop-api", "menuRoute": "", "menuItems": [{"menuTitle": "Audit", "menuRoute": "app/desktop-api/audit"}, {"menuTitle": "Logging", "menuRoute": "app/desktop-api/logging"}, {"menuTitle": "Pdf Builder", "menuRoute": "app/desktop-api/pdf-builder"}, {"menuTitle": "Csv Builder", "menuRoute": "app/desktop-api/csv-builder"}, {"menuTitle": "Rs schema", "menuRoute": "app/desktop-api/rs-schema"}, {"menuTitle": "Transactions", "menuRoute": "app/desktop-api/transactions"}]}, {"menuTitle": "@riverstar/workflow-studio", "menuRoute": "", "menuItems": [{"menuTitle": "Getting started", "menuRoute": "app/workflow-studio/getting-started"}, {"menuTitle": "Setup", "menuRoute": "app/workflow-studio/setup"}, {"menuTitle": "Elements", "menuRoute": "workflow-studio/element"}, {"menuTitle": "Workflows", "menuRoute": "workflow-studio/workflow/list"}, {"menuTitle": "Workflow Menus", "menuRoute": "workflow-studio/workflow-menu/list"}, {"menuTitle": "Workflow Profiles", "menuRoute": "workflow-studio/workflow-profile/list"}, {"menuTitle": "Business rules", "menuRoute": "workflow-studio/business-rule/list"}, {"menuTitle": "Entities", "menuRoute": "workflow-studio/entity"}, {"menuTitle": "Dynamic languages", "menuRoute": "workflow-studio/dynamic-languages/list"}, {"menuTitle": "Security model", "menuRoute": "workflow-studio/security-model/list"}, {"menuTitle": "Settings", "menuRoute": "workflow-studio/settings"}, {"menuTitle": "Stylesheets", "menuRoute": "workflow-studio/stylesheet/list"}]}, {"menuTitle": "@riverstar/campaign-manager", "menuRoute": "", "menuItems": [{"menuTitle": "Getting started", "menuRoute": "app/campaign-manager/getting-started"}, {"menuTitle": "Setup", "menuRoute": "app/campaign-manager/setup"}, {"menuTitle": "Campaigns", "menuRoute": "/desktop/campaign-manager/list"}, {"menuTitle": "Report", "menuRoute": "/desktop/campaign-manager/report"}]}, {"menuTitle": "Migrations", "menuRoute": "", "menuItems": [{"menuTitle": "Migration to Desktop v7", "menuRoute": "app/migrations/migration"}]}, {"menuTitle": "FAQ", "menuRoute": "app/faq"}], "isActive": true, "isDefault": true, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "app/introduction", "createdAt": "2025-02-20T18:54:36.894Z", "updatedAt": "2025-02-20T18:54:36.894Z", "__v": 0}]