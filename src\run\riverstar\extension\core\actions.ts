interface Contact {
  firstName: string;
  lastName: string;
  email: string;
}

const PASSWORD = {
  OFFSET: 9,
  LENGTH: 15,
} as const;

export default {
  "Create Contacts": function () {
    const firstValue = Cypress.sdt.current.step.simpleValues[0];
    const numberOfContacts = parseInt(firstValue);

    if (isNaN(numberOfContacts)) {
      const contact: Contact = {
        firstName: firstValue,
        lastName: Cypress.sdt.current.step.simpleValues[1] ?? "",
        email: `${firstValue}.${
          Cypress.sdt.current.step.simpleValues[1] ?? ""
        }@email.com`,
      };

      Cypress.sdt.apiHandler.createContact(contact);
    } else {
      cy.wrap(Array.from({ length: numberOfContacts })).each((_, index) => {
        const i = index + 1;
        const contact: Contact = {
          firstName: `firstName-${i}`,
          lastName: `lastName-${i}`,
          email: `firstName-${i}.lastName-${i}@email.com`,
        };
        Cypress.sdt.apiHandler.createContact(contact);
      });
    }
  },

  "Type Temp Password": function () {
    cy.task("dbGetLastEmail").then((email: string) => {
      const passwordOffset = email.indexOf("password=") + PASSWORD.OFFSET;
      const newPassword = email.substring(
        passwordOffset,
        passwordOffset + PASSWORD.LENGTH
      );
      cy.get('input[type="password"]').type(newPassword);
    });
  },
};
