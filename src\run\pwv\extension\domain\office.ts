import h from "@/app/helpers/all";

export default class Office {
  id = "";
  name = "";
  address = "";
  address2 = "";
  city = "";
  state = "";
  zip = "";
  isActive: boolean = true;
  email = "";
  phone = null;

  constructor(office) {
    Object.assign(this, h.parseObjectWithTemplate(office, this));
    this.address = office["Full Address"]["Address"];
    this.address2 = office["Full Address"]["Address (cont.)"];
    this.city = office["Full Address"]["City"];
    this.state = office["Full Address"]["State"];
    this.zip = office["Full Address"]["Zip"];
    this.isActive = true;
  }

  create() {
    return Cypress.sdt.apiHandler
      .login(
        Cypress.sdt.config.admin.username,
        Cypress.sdt.config.admin.password
      )
      .then(() => Cypress.sdt.apiHandler.createOffice(this));
  }
}
