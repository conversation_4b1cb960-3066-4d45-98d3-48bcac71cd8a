import extensionActions from "./core/actions";
import extensionApiHandler from "./handlers/apiHandler";
import extensionIcons from "./core/icons";
import h from "@/app/helpers/all";
import riverstarSdt from "@/app/organizations/riverstar/sdt";
import setup from "./core/setup";

export default {
  setup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, extensionIcons),
  actions: { ...riverstarSdt.actions, ...extensionActions },
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, extensionApiHandler),
} as const;
