import h from "@/app/helpers/all";

export default {
  stepsToRun: [],

  doneStepLabelColor: "rgba(0, 0, 0, 0.87)",
  notDoneStepLabelColor: "rgba(0, 0, 0, 0.54)",
  doneStepIconBackgroundColor: "rgb(41, 102, 176)",
  notDoneStepIconBackgroundColor: "rgba(0, 0, 0, 0.54)",

  checkStepper(params) {
    const currentStepIndex = this.getStepIndex(params[0]);

    let lastExecutedStepIndex = currentStepIndex - 1;
    if (params.length === 2) {
      lastExecutedStepIndex = this.getStepIndex(params[1]);
    }

    return this.getStepsToRun().then(() =>
      cy.get(".mat-step-text-label").each(($el) => {
        cy.wrap($el)
          .invoke("text")
          .then((label) => {
            const stepLabel = label;
            this.checkStepperStep(
              stepLabel,
              currentStepIndex,
              lastExecutedStepIndex
            );
          });
      })
    );
  },

  checkStepperStep(stepLabel, currentStepIndex, lastExecutedStepIndex) {
    const stepIndex = this.getStepIndex(stepLabel);
    cy.contains("mat-step-header", stepLabel).within(() => {
      if (stepIndex <= currentStepIndex || stepIndex <= lastExecutedStepIndex) {
        cy.get(".mat-step-label").should(
          "have.css",
          "color",
          this.doneStepLabelColor
        );
        cy.get(".mat-step-icon").should(
          "have.css",
          "background-color",
          this.doneStepIconBackgroundColor
        );
        cy.get(".mat-step-label")
          .realHover()
          .should("have.css", "cursor", "pointer");
      } else {
        cy.get(".mat-step-label").should(
          "have.css",
          "color",
          this.notDoneStepLabelColor
        );
        cy.get(".mat-step-icon").should(
          "have.css",
          "background-color",
          this.notDoneStepIconBackgroundColor
        );
        cy.get(".mat-step-label")
          .realHover()
          .should("have.css", "cursor", "default");
      }
    });
  },

  getStepIndex(stepLabel) {
    const stepLabels = [
      "Schedule Review",
      "Screen",
      "Review Screen",
      "Schedule Interview Review",
      "Interview",
      "Review Interview",
      "Done",
    ];
    return stepLabels.indexOf(
      stepLabels.find((label) => h.compareNormalizedStrings(label, stepLabel))
    );
  },

  getStepsToRun() {
    return cy.get(".mat-step-text-label").each((stepLabel) => {
      this.stepsToRun.push(stepLabel.text());
    });
  },
};
