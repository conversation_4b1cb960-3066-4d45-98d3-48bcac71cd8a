/**
 * Dynamically loads tasks based on the APP environment variable
 */
async function loadTasks() {
  const projectName = process.env.APP;

  if (!projectName) {
    throw new Error(
      "APP environment variable is not set. " +
        "Please run from a project-specific run script that sets this variable."
    );
  }

  try {
    const tasks = await import(`@/run/${projectName}/extension/tasks`);
    return tasks.default;
  } catch (error) {
    throw new Error(
      `Failed to load tasks for project '${projectName}'. ` +
        `Please ensure the tasks file exists at src/run/${projectName}/extension/tasks.ts. ` +
        `Original error: ${error.message}`
    );
  }
}

// Export the dynamic loader function
export default loadTasks;
